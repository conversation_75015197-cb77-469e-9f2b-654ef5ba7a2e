# Install required packages
!pip install google-generativeai pandas numpy matplotlib seaborn scikit-learn

# Import required libraries
import json
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Any
import google.generativeai as genai
from sklearn.metrics import precision_recall_fscore_support
import re
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

# Configure plotting
plt.style.use('default')
sns.set_palette("husl")
plt.rcParams['figure.figsize'] = (12, 8)

# Configure Gemini API
# Replace 'YOUR_API_KEY' with your actual Gemini API key
GEMINI_API_KEY = 'AIzaSyCJaADIHnUd3TmZDfyeh2JKk_k8WO6t7JI'  # Get from https://makersuite.google.com/app/apikey
genai.configure(api_key=GEMINI_API_KEY)

# Initialize Gemini model
model = genai.GenerativeModel('gemini-2.5-flash-preview-05-20')

print("✅ Gemini AI configured successfully!")

# Load data files
def load_json_data(file_path: str) -> Dict:
    """Load JSON data from file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading {file_path}: {e}")
        return {}

# Load ground truth and model response data
ground_truth = load_json_data('ground_truth.json')
model_response = load_json_data('latest.json')

# Extract observations
gt_observations = ground_truth.get('audit_report', {}).get('observations', [])
model_observations = model_response.get('audit_report', {}).get('observations', [])

print(f"📊 Loaded {len(gt_observations)} ground truth observations")
print(f"📊 Loaded {len(model_observations)} model response observations")
print("\n✅ Data loaded successfully!")

def get_gemini_similarity_score(text1: str, text2: str, context: str = "") -> Dict:
    """Get semantic similarity score and analysis from Gemini"""
    prompt = f"""
    Compare these two UX audit observations and provide:
    1. Similarity score (0-100)
    2. Brief analysis of similarities and differences
    3. Whether they refer to the same UX issue (yes/no)

    Context: {context}

    Text 1: {text1}
    Text 2: {text2}

    Respond in JSON format:
    {{
        "similarity_score": <0-100>,
        "same_issue": <true/false>,
        "analysis": "<brief analysis>"
    }}
    """

    try:
        response = model.generate_content(prompt)
        # Extract JSON from response
        json_match = re.search(r'\{.*\}', response.text, re.DOTALL)
        if json_match:
            return json.loads(json_match.group())
        else:
            return {"similarity_score": 0, "same_issue": False, "analysis": "Failed to parse response"}
    except Exception as e:
        print(f"Error in Gemini API call: {e}")
        return {"similarity_score": 0, "same_issue": False, "analysis": f"API Error: {e}"}

print("🔧 Gemini similarity function ready!")

def find_observation_matches_optimized(gt_obs: List[Dict], model_obs: List[Dict]) -> List[Dict]:
    """Find optimal 1:1 matches between GT and model observations using Gemini efficiently"""
    print("🔍 Finding optimal observation matches using Gemini AI...")
    print(f"📊 Processing {len(gt_obs)} GT observations vs {len(model_obs)} model observations")

    # Step 1: Batch similarity analysis for all pairs
    print("\n🚀 Step 1: Batch analyzing all GT-Model pairs...")

    similarity_matrix = []

    # Create batch prompt for all comparisons to reduce API calls
    batch_prompt = f"""
    Compare these UX audit observations and provide similarity scores for each pair.
    For each comparison, consider both location similarity and observation content similarity.

    GROUND TRUTH OBSERVATIONS:
    {json.dumps([{"id": i, "location": gt["location"], "observation": gt["observation"][:200] + "..."} for i, gt in enumerate(gt_obs)], indent=2)}

    MODEL RESPONSE OBSERVATIONS:
    {json.dumps([{"id": i, "location": model["location"], "observation": model["observation"][:200] + "..."} for i, model in enumerate(model_obs)], indent=2)}

    For each GT observation, find the best matching model observation and provide:

    Respond in JSON format:
    {{
        "matches": [
            {{
                "gt_id": <gt_index>,
                "best_model_id": <model_index or -1 if no good match>,
                "location_similarity": <0-100>,
                "observation_similarity": <0-100>,
                "combined_score": <0-100>,
                "is_valid_match": <true/false>,
                "reasoning": "brief explanation"
            }}
        ]
    }}

    Only consider matches with combined_score > 40. Set best_model_id to -1 if no good match exists.
    """

    try:
        print("🤖 Sending batch request to Gemini...")
        response = model.generate_content(batch_prompt)

        # Extract JSON from response
        json_match = re.search(r'\{.*\}', response.text, re.DOTALL)
        if json_match:
            batch_results = json.loads(json_match.group())
            potential_matches = batch_results.get('matches', [])
            print(f"✅ Batch analysis complete! Found {len(potential_matches)} potential matches")
        else:
            print("❌ Failed to parse batch response, falling back to individual analysis")
            potential_matches = []
    except Exception as e:
        print(f"❌ Batch analysis failed: {e}")
        print("🔄 Falling back to optimized individual analysis...")
        potential_matches = []

    # Step 2: If batch failed, use optimized individual analysis
    if not potential_matches:
        potential_matches = []
        for i, gt_item in enumerate(gt_obs):
            print(f"\n📍 Processing GT {i+1}/{len(gt_obs)}: {gt_item['location'][:30]}...")

            # Quick location-based filtering first
            candidate_models = []
            for j, model_item in enumerate(model_obs):
                # Simple keyword overlap check for initial filtering
                gt_keywords = set(gt_item['location'].lower().split())
                model_keywords = set(model_item['location'].lower().split())
                keyword_overlap = len(gt_keywords.intersection(model_keywords)) / max(len(gt_keywords), 1)

                if keyword_overlap > 0.2:  # At least 20% keyword overlap
                    candidate_models.append((j, model_item, keyword_overlap))

            # Sort by keyword overlap and take top 3 candidates
            candidate_models.sort(key=lambda x: x[2], reverse=True)
            top_candidates = candidate_models[:3]

            if not top_candidates:
                print(f"  ❌ No location candidates found")
                potential_matches.append({
                    'gt_id': i,
                    'best_model_id': -1,
                    'combined_score': 0,
                    'is_valid_match': False,
                    'reasoning': 'No location similarity found'
                })
                continue

            # Use Gemini only for top candidates
            best_match = None
            best_score = 0

            candidates_text = "\n".join([f"Model {idx}: {item['location']} - {item['observation'][:100]}..."
                                        for idx, item, _ in top_candidates])

            candidate_prompt = f"""
            Find the best match for this GT observation among the candidates:

            GT Observation:
            Location: {gt_item['location']}
            Description: {gt_item['observation'][:200]}...

            Candidates:
            {candidates_text}

            Respond in JSON format:
            {{
                "best_model_id": <model_index or -1>,
                "location_similarity": <0-100>,
                "observation_similarity": <0-100>,
                "combined_score": <0-100>,
                "is_valid_match": <true/false>,
                "reasoning": "brief explanation"
            }}
            """

            try:
                response = model.generate_content(candidate_prompt)
                json_match = re.search(r'\{.*\}', response.text, re.DOTALL)
                if json_match:
                    result = json.loads(json_match.group())
                    result['gt_id'] = i
                    potential_matches.append(result)
                    if result['is_valid_match']:
                        print(f"  ✅ Match found: Model {result['best_model_id']} (Score: {result['combined_score']})")
                    else:
                        print(f"  ❌ No valid match (Best score: {result['combined_score']})")
                else:
                    print(f"  ❌ Failed to parse response")
            except Exception as e:
                print(f"  ❌ Error: {e}")

    # Step 3: Resolve conflicts and create optimal 1:1 matching
    print("\n🎯 Step 3: Resolving conflicts for optimal 1:1 matching...")

    # Filter valid matches and sort by score
    valid_matches = [m for m in potential_matches if m['is_valid_match'] and m['best_model_id'] != -1]
    valid_matches.sort(key=lambda x: x['combined_score'], reverse=True)

    # Resolve conflicts using Hungarian-like approach (greedy for simplicity)
    used_model_ids = set()
    used_gt_ids = set()
    final_matches = []

    for match in valid_matches:
        gt_id = match['gt_id']
        model_id = match['best_model_id']

        if gt_id not in used_gt_ids and model_id not in used_model_ids:
            # Create final match object
            final_match = {
                'gt_index': gt_id,
                'model_index': model_id,
                'gt_item': gt_obs[gt_id],
                'model_item': model_obs[model_id],
                'location_similarity': {'similarity_score': match['location_similarity']},
                'observation_similarity': {'similarity_score': match['observation_similarity']},
                'combined_score': match['combined_score'],
                'reasoning': match['reasoning']
            }
            final_matches.append(final_match)
            used_gt_ids.add(gt_id)
            used_model_ids.add(model_id)

    print(f"\n🎯 Final Results: {len(final_matches)} optimal 1:1 matches found")
    print(f"📊 GT Coverage: {len(final_matches)}/{len(gt_obs)} ({len(final_matches)/len(gt_obs)*100:.1f}%)")
    print(f"📊 Model Coverage: {len(final_matches)}/{len(model_obs)} ({len(final_matches)/len(model_obs)*100:.1f}%)")

    return final_matches

# Find optimal matches between observations
observation_matches = find_observation_matches_optimized(gt_observations, model_observations)

def calculate_observation_coverage(matches: List[Dict], total_gt: int) -> Dict:
    """Calculate observation coverage metrics"""
    coverage_score = len(matches) / total_gt if total_gt > 0 else 0

    coverage_analysis = {
        'total_gt_observations': total_gt,
        'matched_observations': len(matches),
        'coverage_percentage': coverage_score * 100,
        'unmatched_observations': total_gt - len(matches)
    }

    return coverage_analysis

# Calculate coverage
coverage_results = calculate_observation_coverage(observation_matches, len(gt_observations))

print("📊 OBSERVATION COVERAGE ANALYSIS")
print("=" * 40)
print(f"Total Ground Truth Observations: {coverage_results['total_gt_observations']}")
print(f"Successfully Matched: {coverage_results['matched_observations']}")
print(f"Coverage Percentage: {coverage_results['coverage_percentage']:.1f}%")
print(f"Unmatched Observations: {coverage_results['unmatched_observations']}")

# Visualize coverage
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

# Coverage pie chart
labels = ['Matched', 'Unmatched']
sizes = [coverage_results['matched_observations'], coverage_results['unmatched_observations']]
colors = ['#2ecc71', '#e74c3c']
ax1.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
ax1.set_title('Observation Coverage', fontsize=14, fontweight='bold')

# Coverage bar chart
categories = ['Coverage %', 'Missing %']
values = [coverage_results['coverage_percentage'], 100 - coverage_results['coverage_percentage']]
bars = ax2.bar(categories, values, color=colors)
ax2.set_ylabel('Percentage')
ax2.set_title('Coverage vs Missing Observations', fontsize=14, fontweight='bold')
ax2.set_ylim(0, 100)

# Add value labels on bars
for bar, value in zip(bars, values):
    height = bar.get_height()
    ax2.text(bar.get_x() + bar.get_width()/2., height + 1,
             f'{value:.1f}%', ha='center', va='bottom', fontweight='bold')

plt.tight_layout()
plt.show()

def analyze_heuristic_accuracy_with_gemini(matches: List[Dict]) -> Dict:
    """Analyze heuristic accuracy using Gemini for semantic comparison"""
    print("🧠 Analyzing heuristic accuracy using Gemini AI...")

    heuristic_results = []

    for i, match in enumerate(matches):
        gt_heuristics = match['gt_item']['heuristics_violated']
        model_heuristics = match['model_item']['heuristics_violated']

        print(f"\n🔍 Analyzing match {i+1}/{len(matches)}...")

        # Use Gemini to compare heuristic lists
        heuristic_prompt = f"""
        Compare these two lists of UX heuristics violated for the same issue.
        Analyze semantic similarity and provide detailed comparison.

        Ground Truth Heuristics: {gt_heuristics}
        Model Response Heuristics: {model_heuristics}

        Context - Issue Location: {match['gt_item']['location']}
        Context - Issue Description: {match['gt_item']['observation'][:200]}...

        Provide analysis in JSON format:
        {{
            "semantic_overlap_score": <0-100>,
            "correctly_identified": ["list of semantically matching heuristics"],
            "missed_heuristics": ["list of GT heuristics not captured by model"],
            "extra_heuristics": ["list of model heuristics not in GT"],
            "precision_score": <0-100>,
            "recall_score": <0-100>,
            "analysis": "detailed analysis of the comparison"
        }}
        """

        try:
            response = model.generate_content(heuristic_prompt)
            json_match = re.search(r'\{.*\}', response.text, re.DOTALL)
            if json_match:
                heuristic_analysis = json.loads(json_match.group())
                heuristic_analysis['match_index'] = i
                heuristic_analysis['gt_location'] = match['gt_item']['location']
                heuristic_results.append(heuristic_analysis)
                print(f"✅ Analysis complete - Overlap: {heuristic_analysis['semantic_overlap_score']}%")
            else:
                print("❌ Failed to parse Gemini response")
        except Exception as e:
            print(f"❌ Error in heuristic analysis: {e}")

    return heuristic_results

# Analyze heuristic accuracy
heuristic_analysis = analyze_heuristic_accuracy_with_gemini(observation_matches)

# Display heuristic analysis results
if heuristic_analysis:
    print("\n🧠 HEURISTIC ACCURACY ANALYSIS")
    print("=" * 40)

    # Calculate overall metrics
    avg_precision = np.mean([h['precision_score'] for h in heuristic_analysis])
    avg_recall = np.mean([h['recall_score'] for h in heuristic_analysis])
    avg_overlap = np.mean([h['semantic_overlap_score'] for h in heuristic_analysis])

    print(f"Average Precision: {avg_precision:.1f}%")
    print(f"Average Recall: {avg_recall:.1f}%")
    print(f"Average Semantic Overlap: {avg_overlap:.1f}%")

    # Visualize heuristic accuracy
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

    # Precision and Recall scores
    locations = [h['gt_location'][:20] + '...' for h in heuristic_analysis]
    precision_scores = [h['precision_score'] for h in heuristic_analysis]
    recall_scores = [h['recall_score'] for h in heuristic_analysis]

    x = np.arange(len(locations))
    width = 0.35

    ax1.bar(x - width/2, precision_scores, width, label='Precision', color='#3498db')
    ax1.bar(x + width/2, recall_scores, width, label='Recall', color='#e74c3c')
    ax1.set_xlabel('Matched Observations')
    ax1.set_ylabel('Score (%)')
    ax1.set_title('Heuristic Precision & Recall by Observation')
    ax1.set_xticks(x)
    ax1.set_xticklabels(locations, rotation=45, ha='right')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # Semantic overlap scores
    overlap_scores = [h['semantic_overlap_score'] for h in heuristic_analysis]
    bars = ax2.bar(locations, overlap_scores, color='#2ecc71')
    ax2.set_xlabel('Matched Observations')
    ax2.set_ylabel('Overlap Score (%)')
    ax2.set_title('Semantic Overlap of Heuristics')
    ax2.set_xticklabels(locations, rotation=45, ha='right')
    ax2.grid(True, alpha=0.3)

    # Overall metrics comparison
    metrics = ['Precision', 'Recall', 'Semantic Overlap']
    values = [avg_precision, avg_recall, avg_overlap]
    colors = ['#3498db', '#e74c3c', '#2ecc71']
    bars = ax3.bar(metrics, values, color=colors)
    ax3.set_ylabel('Average Score (%)')
    ax3.set_title('Overall Heuristic Analysis Metrics')
    ax3.set_ylim(0, 100)

    # Add value labels on bars
    for bar, value in zip(bars, values):
        height = bar.get_height()
        ax3.text(bar.get_x() + bar.get_width()/2., height + 1,
                 f'{value:.1f}%', ha='center', va='bottom', fontweight='bold')

    # Distribution of overlap scores
    ax4.hist(overlap_scores, bins=10, color='#9b59b6', alpha=0.7, edgecolor='black')
    ax4.set_xlabel('Semantic Overlap Score (%)')
    ax4.set_ylabel('Frequency')
    ax4.set_title('Distribution of Semantic Overlap Scores')
    ax4.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.show()
else:
    print("❌ No heuristic analysis results available")

def analyze_severity_consistency_with_gemini(matches: List[Dict]) -> Dict:
    """Analyze severity consistency using Gemini"""
    print("⚖️ Analyzing severity consistency using Gemini AI...")

    severity_results = []

    for i, match in enumerate(matches):
        gt_severity = match['gt_item']['severity']
        model_severity = match['model_item']['severity']

        print(f"\n🔍 Analyzing severity for match {i+1}/{len(matches)}...")

        # Use Gemini to analyze severity appropriateness
        severity_prompt = f"""
        Analyze the severity rating consistency for this UX issue.

        Issue Location: {match['gt_item']['location']}
        Issue Description: {match['gt_item']['observation']}

        Ground Truth Severity: {gt_severity}
        Model Response Severity: {model_severity}

        Provide analysis in JSON format:
        {{
            "severity_match": <true/false>,
            "severity_appropriateness_score": <0-100>,
            "gt_severity_justified": <true/false>,
            "model_severity_justified": <true/false>,
            "severity_gap_analysis": "explanation of any severity differences",
            "recommended_severity": "High/Medium/Low",
            "analysis": "detailed analysis of severity consistency"
        }}
        """

        try:
            response = model.generate_content(severity_prompt)
            json_match = re.search(r'\{.*\}', response.text, re.DOTALL)
            if json_match:
                severity_analysis = json.loads(json_match.group())
                severity_analysis['match_index'] = i
                severity_analysis['gt_severity'] = gt_severity
                severity_analysis['model_severity'] = model_severity
                severity_analysis['gt_location'] = match['gt_item']['location']
                severity_results.append(severity_analysis)
                print(f"✅ Severity analysis complete - Match: {severity_analysis['severity_match']}")
            else:
                print("❌ Failed to parse Gemini response")
        except Exception as e:
            print(f"❌ Error in severity analysis: {e}")

    return severity_results

# Analyze severity consistency
severity_analysis = analyze_severity_consistency_with_gemini(observation_matches)

# Display severity analysis results
if severity_analysis:
    print("\n⚖️ SEVERITY CONSISTENCY ANALYSIS")
    print("=" * 40)

    # Calculate metrics
    exact_matches = sum(1 for s in severity_analysis if s['severity_match'])
    match_percentage = (exact_matches / len(severity_analysis)) * 100 if severity_analysis else 0
    avg_appropriateness = np.mean([s['severity_appropriateness_score'] for s in severity_analysis])

    print(f"Exact Severity Matches: {exact_matches}/{len(severity_analysis)} ({match_percentage:.1f}%)")
    print(f"Average Appropriateness Score: {avg_appropriateness:.1f}%")

    # Create severity comparison visualization
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

    # Severity match pie chart
    match_labels = ['Exact Match', 'Different']
    match_sizes = [exact_matches, len(severity_analysis) - exact_matches]
    match_colors = ['#2ecc71', '#e74c3c']
    ax1.pie(match_sizes, labels=match_labels, colors=match_colors, autopct='%1.1f%%', startangle=90)
    ax1.set_title('Severity Rating Matches', fontsize=14, fontweight='bold')

    # Appropriateness scores
    locations = [s['gt_location'][:15] + '...' for s in severity_analysis]
    appropriateness_scores = [s['severity_appropriateness_score'] for s in severity_analysis]
    bars = ax2.bar(locations, appropriateness_scores, color='#3498db')
    ax2.set_xlabel('Matched Observations')
    ax2.set_ylabel('Appropriateness Score (%)')
    ax2.set_title('Severity Appropriateness by Observation')
    ax2.set_xticklabels(locations, rotation=45, ha='right')
    ax2.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.show()

    # Display detailed severity comparison
    print("\n📋 Detailed Severity Comparison:")
    for i, s in enumerate(severity_analysis):
        status = "✅" if s['severity_match'] else "❌"
        print(f"{status} {s['gt_location'][:30]}... | GT: {s['gt_severity']} | Model: {s['model_severity']} | Score: {s['severity_appropriateness_score']}%")
else:
    print("❌ No severity analysis results available")

def analyze_false_positives_negatives_with_gemini(gt_obs: List[Dict], model_obs: List[Dict], matches: List[Dict]) -> Dict:
    """Analyze false positives and negatives using Gemini"""
    print("🔍 Analyzing false positives and negatives using Gemini AI...")

    # Get matched indices
    matched_gt_indices = {match['gt_index'] for match in matches}
    matched_model_indices = {match['model_index'] for match in matches}

    # False negatives: GT observations not matched
    false_negatives = [obs for i, obs in enumerate(gt_obs) if i not in matched_gt_indices]

    # False positives: Model observations not matched
    false_positives = [obs for i, obs in enumerate(model_obs) if i not in matched_model_indices]

    print(f"\n📊 Found {len(false_negatives)} false negatives (missed GT issues)")
    print(f"📊 Found {len(false_positives)} false positives (model hallucinations)")

    # Analyze false negatives with Gemini
    fn_analysis = []
    if false_negatives:
        print("\n🔍 Analyzing false negatives...")
        for i, fn in enumerate(false_negatives):
            fn_prompt = f"""
            Analyze why this ground truth UX issue might have been missed by the model.

            Missed Issue:
            Location: {fn['location']}
            Severity: {fn['severity']}
            Observation: {fn['observation']}
            Heuristics: {fn['heuristics_violated']}

            Provide analysis in JSON format:
            {{
                "issue_complexity": <1-10>,
                "issue_visibility": <1-10>,
                "likely_reasons_missed": ["list of reasons"],
                "impact_of_missing": "High/Medium/Low",
                "analysis": "detailed analysis of why this was missed"
            }}
            """

            try:
                response = model.generate_content(fn_prompt)
                json_match = re.search(r'\{.*\}', response.text, re.DOTALL)
                if json_match:
                    fn_result = json.loads(json_match.group())
                    fn_result['gt_item'] = fn
                    fn_analysis.append(fn_result)
                    print(f"✅ FN analysis {i+1}/{len(false_negatives)} complete")
            except Exception as e:
                print(f"❌ Error analyzing FN {i+1}: {e}")

    # Analyze false positives with Gemini
    fp_analysis = []
    if false_positives:
        print("\n🔍 Analyzing false positives...")
        for i, fp in enumerate(false_positives):
            fp_prompt = f"""
            Analyze whether this model-generated UX issue is a valid concern or a hallucination.

            Model Issue:
            Location: {fp['location']}
            Severity: {fp['severity']}
            Observation: {fp['observation']}
            Heuristics: {fp['heuristics_violated']}

            Provide analysis in JSON format:
            {{
                "is_valid_issue": <true/false>,
                "validity_confidence": <0-100>,
                "issue_quality": <1-10>,
                "potential_value": "High/Medium/Low",
                "classification": "Valid Addition/Minor Issue/Hallucination",
                "analysis": "detailed analysis of the issue validity"
            }}
            """

            try:
                response = model.generate_content(fp_prompt)
                json_match = re.search(r'\{.*\}', response.text, re.DOTALL)
                if json_match:
                    fp_result = json.loads(json_match.group())
                    fp_result['model_item'] = fp
                    fp_analysis.append(fp_result)
                    print(f"✅ FP analysis {i+1}/{len(false_positives)} complete")
            except Exception as e:
                print(f"❌ Error analyzing FP {i+1}: {e}")

    return {
        'false_negatives': false_negatives,
        'false_positives': false_positives,
        'fn_analysis': fn_analysis,
        'fp_analysis': fp_analysis
    }

# Analyze false positives and negatives
fp_fn_analysis = analyze_false_positives_negatives_with_gemini(gt_observations, model_observations, observation_matches)

# Display false positives and negatives analysis
print("\n🔍 FALSE POSITIVES & NEGATIVES ANALYSIS")
print("=" * 50)

print(f"\n❌ FALSE NEGATIVES (Missed GT Issues): {len(fp_fn_analysis['false_negatives'])}")
for i, fn in enumerate(fp_fn_analysis['false_negatives']):
    print(f"  {i+1}. {fn['location']} | Severity: {fn['severity']}")
    print(f"     {fn['observation'][:100]}...")

print(f"\n➕ FALSE POSITIVES (Model Additions): {len(fp_fn_analysis['false_positives'])}")
for i, fp in enumerate(fp_fn_analysis['false_positives']):
    print(f"  {i+1}. {fp['location']} | Severity: {fp['severity']}")
    print(f"     {fp['observation'][:100]}...")

# Visualize false positives and negatives
if fp_fn_analysis['fn_analysis'] or fp_fn_analysis['fp_analysis']:
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

    # False negatives impact analysis
    if fp_fn_analysis['fn_analysis']:
        fn_impacts = [fn['impact_of_missing'] for fn in fp_fn_analysis['fn_analysis']]
        fn_impact_counts = {impact: fn_impacts.count(impact) for impact in ['High', 'Medium', 'Low']}
        ax1.bar(fn_impact_counts.keys(), fn_impact_counts.values(), color=['#e74c3c', '#f39c12', '#2ecc71'])
        ax1.set_title('Impact of Missing Issues (False Negatives)')
        ax1.set_ylabel('Count')

    # False positives validity analysis
    if fp_fn_analysis['fp_analysis']:
        fp_classifications = [fp['classification'] for fp in fp_fn_analysis['fp_analysis']]
        fp_class_counts = {}
        for classification in fp_classifications:
            fp_class_counts[classification] = fp_class_counts.get(classification, 0) + 1
        ax2.bar(fp_class_counts.keys(), fp_class_counts.values(), color=['#3498db', '#9b59b6', '#e67e22'])
        ax2.set_title('Classification of Model Additions (False Positives)')
        ax2.set_ylabel('Count')
        ax2.tick_params(axis='x', rotation=45)

    # Overall false positive/negative distribution
    categories = ['True Positives\n(Matches)', 'False Negatives\n(Missed)', 'False Positives\n(Added)']
    values = [len(observation_matches), len(fp_fn_analysis['false_negatives']), len(fp_fn_analysis['false_positives'])]
    colors = ['#2ecc71', '#e74c3c', '#f39c12']
    bars = ax3.bar(categories, values, color=colors)
    ax3.set_title('Overall Issue Detection Performance')
    ax3.set_ylabel('Count')

    # Add value labels on bars
    for bar, value in zip(bars, values):
        height = bar.get_height()
        ax3.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                 str(value), ha='center', va='bottom', fontweight='bold')

    # Performance metrics pie chart
    total_gt = len(gt_observations)
    total_model = len(model_observations)
    precision = len(observation_matches) / total_model if total_model > 0 else 0
    recall = len(observation_matches) / total_gt if total_gt > 0 else 0
    f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0

    metrics = ['Precision', 'Recall', 'F1-Score']
    metric_values = [precision * 100, recall * 100, f1_score * 100]
    bars = ax4.bar(metrics, metric_values, color=['#3498db', '#e74c3c', '#2ecc71'])
    ax4.set_title('Overall Detection Metrics')
    ax4.set_ylabel('Score (%)')
    ax4.set_ylim(0, 100)

    # Add value labels on bars
    for bar, value in zip(bars, metric_values):
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height + 1,
                 f'{value:.1f}%', ha='center', va='bottom', fontweight='bold')

    plt.tight_layout()
    plt.show()

print(f"\n📊 DETECTION PERFORMANCE METRICS")
print("=" * 40)
total_gt = len(gt_observations)
total_model = len(model_observations)
matches_count = len(observation_matches)
precision = matches_count / total_model if total_model > 0 else 0
recall = matches_count / total_gt if total_gt > 0 else 0
f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0

print(f"Precision: {precision:.3f} ({precision*100:.1f}%)")
print(f"Recall: {recall:.3f} ({recall*100:.1f}%)")
print(f"F1-Score: {f1_score:.3f} ({f1_score*100:.1f}%)")
print(f"Total GT Issues: {total_gt}")
print(f"Total Model Issues: {total_model}")
print(f"Successfully Matched: {matches_count}")

def generate_overall_evaluation_summary(coverage_results, heuristic_analysis, severity_analysis, fp_fn_analysis, observation_matches) -> str:
    """Generate comprehensive evaluation summary using Gemini"""
    print("📝 Generating overall evaluation summary using Gemini AI...")

    # Prepare summary data
    total_gt = len(gt_observations)
    total_model = len(model_observations)
    matches_count = len(observation_matches)

    # Calculate metrics
    coverage_pct = coverage_results['coverage_percentage']
    avg_heuristic_precision = np.mean([h['precision_score'] for h in heuristic_analysis]) if heuristic_analysis else 0
    avg_heuristic_recall = np.mean([h['recall_score'] for h in heuristic_analysis]) if heuristic_analysis else 0
    severity_match_pct = (sum(1 for s in severity_analysis if s['severity_match']) / len(severity_analysis) * 100) if severity_analysis else 0

    summary_prompt = f"""
    Generate a comprehensive evaluation summary for a UX audit model performance.

    EVALUATION RESULTS:

    1. OBSERVATION COVERAGE:
    - Total Ground Truth Issues: {total_gt}
    - Total Model Issues: {total_model}
    - Successfully Matched: {matches_count}
    - Coverage Percentage: {coverage_pct:.1f}%

    2. HEURISTIC ACCURACY:
    - Average Precision: {avg_heuristic_precision:.1f}%
    - Average Recall: {avg_heuristic_recall:.1f}%

    3. SEVERITY CONSISTENCY:
    - Exact Severity Matches: {severity_match_pct:.1f}%

    4. FALSE POSITIVES/NEGATIVES:
    - False Negatives (Missed): {len(fp_fn_analysis['false_negatives'])}
    - False Positives (Added): {len(fp_fn_analysis['false_positives'])}

    Provide a comprehensive evaluation summary including:
    1. Overall model performance assessment
    2. Key strengths identified
    3. Major weaknesses and areas for improvement
    4. Specific recommendations for model enhancement
    5. Comparative analysis against expected performance
    6. Final grade/rating (A-F scale)

    Format as a detailed professional evaluation report.
    """

    try:
        response = model.generate_content(summary_prompt)
        return response.text
    except Exception as e:
        return f"Error generating summary: {e}"

# Generate overall evaluation summary
evaluation_summary = generate_overall_evaluation_summary(
    coverage_results, heuristic_analysis, severity_analysis, fp_fn_analysis, observation_matches
)

print("\n" + "=" * 80)
print("📋 COMPREHENSIVE EVALUATION SUMMARY")
print("=" * 80)
print(evaluation_summary)
print("=" * 80)

# Compile all results
final_results = {
    'evaluation_metadata': {
        'timestamp': pd.Timestamp.now().isoformat(),
        'total_gt_observations': len(gt_observations),
        'total_model_observations': len(model_observations),
        'evaluation_parameters': [
            'Observation Coverage',
            'Heuristic Match Accuracy',
            'Severity Consistency',
            'Location Accuracy',
            'Observation Similarity',
            'False Positives/Hallucinations',
            'False Negatives/Omissions',
            'Overall Evaluation Summary'
        ]
    },
    'observation_coverage': coverage_results,
    'observation_matches': observation_matches,
    'heuristic_analysis': heuristic_analysis,
    'severity_analysis': severity_analysis,
    'false_positives_negatives': fp_fn_analysis,
    'evaluation_summary': evaluation_summary,
    'performance_metrics': {
        'precision': matches_count / total_model if total_model > 0 else 0,
        'recall': matches_count / total_gt if total_gt > 0 else 0,
        'f1_score': f1_score,
        'coverage_percentage': coverage_results['coverage_percentage'] if coverage_results else 0,
        'severity_match_percentage': (sum(1 for s in severity_analysis if s['severity_match']) / len(severity_analysis) * 100) if severity_analysis else 0,
        'avg_heuristic_precision': np.mean([h['precision_score'] for h in heuristic_analysis]) if heuristic_analysis else 0,
        'avg_heuristic_recall': np.mean([h['recall_score'] for h in heuristic_analysis]) if heuristic_analysis else 0
    }
}

# Save results to JSON file
output_filename = f'ux_audit_evaluation_results_{pd.Timestamp.now().strftime("%Y%m%d_%H%M%S")}.json'
with open(output_filename, 'w', encoding='utf-8') as f:
    json.dump(final_results, f, indent=2, ensure_ascii=False, default=str)

print(f"\n💾 Evaluation results saved to: {output_filename}")
print("\n✅ UX Audit Evaluation Complete!")
print("\n📊 FINAL SUMMARY:")
print(f"   • Coverage: {final_results['performance_metrics']['coverage_percentage']:.1f}%")
print(f"   • Precision: {(final_results['performance_metrics']['precision'] * 100):.1f}%")
print(f"   • Recall: {(final_results['performance_metrics']['recall'] * 100):.1f}%")
print(f"   • F1-Score: {(final_results['performance_metrics']['f1_score'] * 100):.1f}%")
print(f"   • Heuristic Precision: {final_results['performance_metrics']['avg_heuristic_precision']:.1f}%")
print(f"   • Severity Consistency: {final_results['performance_metrics']['severity_match_percentage']:.1f}%")



# Generate overall evaluation summary
evaluation_summary = generate_overall_evaluation_summary(
    coverage_results, heuristic_analysis, severity_analysis, fp_fn_analysis, observation_matches
)

# Compile all results
final_results = {
    'evaluation_metadata': {
        'timestamp': pd.Timestamp.now().isoformat(),
        'total_gt_observations': len(gt_observations),
        'total_model_observations': len(model_observations),
        'evaluation_parameters': [
            'Observation Coverage',
            'Heuristic Match Accuracy',
            'Severity Consistency',
            'Location Accuracy',
            'Observation Similarity',
            'False Positives/Hallucinations',
            'False Negatives/Omissions',
            'Overall Evaluation Summary'
        ]
    },
    'observation_coverage': coverage_results,
    'observation_matches': observation_matches,
    'heuristic_analysis': heuristic_analysis,
    'severity_analysis': severity_analysis,
    'false_positives_negatives': fp_fn_analysis,
    'evaluation_summary': evaluation_summary,
    'performance_metrics': {
        'precision': matches_count / total_model if total_model > 0 else 0,
        'recall': matches_count / total_gt if total_gt > 0 else 0,
        'f1_score': f1_score,
        'coverage_percentage': coverage_results['coverage_percentage'] if coverage_results else 0,
        'severity_match_percentage': (sum(1 for s in severity_analysis if s['severity_match']) / len(severity_analysis) * 100) if severity_analysis else 0,
        'avg_heuristic_precision': np.mean([h['precision_score'] for h in heuristic_analysis]) if heuristic_analysis else 0,
        'avg_heuristic_recall': np.mean([h['recall_score'] for h in heuristic_analysis]) if heuristic_analysis else 0
    }
}

# Save results to JSON file
output_filename = f'ux_audit_evaluation_results_{pd.Timestamp.now().strftime("%Y%m%d_%H%M%S")}.json'
with open(output_filename, 'w', encoding='utf-8') as f:
    json.dump(final_results, f, indent=2, ensure_ascii=False, default=str)

print(f"\n💾 Evaluation results saved to: {output_filename}")
print("\n✅ UX Audit Evaluation Complete!")
print("\n📊 FINAL SUMMARY:")
print(f"   • Coverage: {final_results['performance_metrics']['coverage_percentage']:.1f}%")
print(f"   • Precision: {(final_results['performance_metrics']['precision'] * 100):.1f}%")
print(f"   • Recall: {(final_results['performance_metrics']['recall'] * 100):.1f}%")
print(f"   • F1-Score: {(final_results['performance_metrics']['f1_score'] * 100):.1f}%")
print(f"   • Heuristic Precision: {final_results['performance_metrics']['avg_heuristic_precision']:.1f}%")
print(f"   • Severity Consistency: {final_results['performance_metrics']['severity_match_percentage']:.1f}%")

